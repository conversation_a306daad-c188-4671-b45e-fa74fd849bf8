<!DOCTYPE html>
<html>

<head>
  <!--
    If you are serving your web app in a path other than the root, change the
    href value below to reflect the base path you are serving from.

    The path provided below has to start and end with a slash "/" in order for
    it to work correctly.

    For more details:
    * https://developer.mozilla.org/en-US/docs/Web/HTML/Element/base

    This is a placeholder for base href that will be replaced by the value of
    the `--base-href` argument provided to `flutter build`.
  -->
  <base href="$FLUTTER_BASE_HREF">
  <!--<base href="./">-->

  <meta charset="UTF-8">
  <meta content="IE=Edge" http-equiv="X-UA-Compatible">
  <meta name="description" content="A new Flutter project.">

  <!-- iOS meta tags & icons -->
  <meta name="apple-mobile-web-app-capable" content="yes">
  <meta name="apple-mobile-web-app-status-bar-style" content="black">
  <meta name="apple-mobile-web-app-title" content="logestics">
  <link rel="apple-touch-icon" href="icons/Icon-192.png">
  <!-- <link rel="apple-touch-icon" href="./icons/Icon-192.png">-->

  <!-- Favicon -->
  <link rel="icon" type="image/png" href="favicon.png" />
  <!-- <link rel="icon" type="image/png" href="./favicon.png"/>-->

  <title>logestics</title>
  <!-- <link rel="manifest" href="manifest.json">-->
  <link rel="manifest" href="./manifest.json">

  <!-- PDF.js configuration for printing package -->
  <script>
    var dartPdfJsVersion = "3.2.146";
    var dartPdfJsBaseUrl = "https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.2.146/";
  </script>

  <!-- Handle 404 issues on page reload -->
  <script>
    // Single Page App handling for direct URL access
    (function () {
      // Detect if this is a direct load of a route (not from /)
      if (window.location.pathname !== '/' && window.location.pathname !== '/index.html') {
        // Store the requested URL for the Flutter app to use
        window.sessionStorage.setItem('initialPath', window.location.pathname);

        // Check if the user is logged in (using localStorage as a simple check)
        var isUserLoggedIn = localStorage.getItem('user') !== null;

        // Redirect to the appropriate page
        if (!isUserLoggedIn) {
          window.location.replace('/');
        }
      }
    })();
  </script>
</head>

<body>
  <!-- Flutter Web Configuration -->
  <script>
    // Configure Flutter web renderer
    window.flutterConfiguration = {
      canvasKitBaseUrl: "https://unpkg.com/canvaskit-wasm@0.39.1/bin/",
      // Alternative: Use HTML renderer as fallback
      // renderer: "html"
    };
  </script>
  <script src="flutter_bootstrap.js" async></script>
  <!-- <script src="./flutter_bootstrap.js" async></script>-->
</body>

</html>