import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';

void main() {
  group('FlexibleFormulaCalculationService', () {
    late InvoiceModel testInvoice;

    setUp(() {
      testInvoice = InvoiceModel(
        invoiceNumber: 1001,
        invoiceStatus: 'Active',
        tasNumber: 'TAS001',
        productName: 'Test Product',
        numberOfBags: 100,
        weightPerBag: 50.0, // 50 kg per bag
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 150.0,
        consignorPickUpAddress: 'Test Address',
      );
    });

    test('should calculate standard formula correctly', () {
      // Create standard formula
      final formula = CalculationFormulaModel.fromJson(FormulaTemplates.standardFormula);
      const rateValue = 2.5;

      // Calculate using formula
      final result = FlexibleFormulaCalculationService.calculateWithFormula(
        invoice: testInvoice,
        formula: formula,
        rateValue: rateValue,
      );

      // Expected calculation: (100 bags × 50 kg/bag) ÷ 1000 × 150 km × 2.5 rate = 1875
      const expectedResult = 5.0 * 150.0 * 2.5; // 1875.0
      
      expect(result, isNotNull);
      expect(result, equals(expectedResult));
    });

    test('should calculate multi-step formula correctly', () {
      // Create multi-step formula
      final formula = CalculationFormulaModel.fromJson(FormulaTemplates.multiStepFormula);
      const rateValue = 2.5;

      // Calculate using formula
      final result = FlexibleFormulaCalculationService.calculateWithFormula(
        invoice: testInvoice,
        formula: formula,
        rateValue: rateValue,
      );

      // Expected calculation:
      // Step 1: 150 km × 2.5 rate = 375 (weightRate)
      // Step 2: 5.0 tons × 375 weightRate = 1875
      const expectedResult = 5.0 * (150.0 * 2.5); // 1875.0
      
      expect(result, isNotNull);
      expect(result, equals(expectedResult));
    });

    test('should calculate markup formula correctly', () {
      // Create markup formula
      final formula = CalculationFormulaModel.fromJson(FormulaTemplates.markupFormula);
      const rateValue = 2.5;

      // Calculate using formula
      final result = FlexibleFormulaCalculationService.calculateWithFormula(
        invoice: testInvoice,
        formula: formula,
        rateValue: rateValue,
      );

      // Expected calculation:
      // Step 1: 5.0 tons × 150 km × 2.5 rate = 1875 (baseAmount)
      // Step 2: 1875 × 1.1 = 2062.5 (10% markup)
      const expectedResult = (5.0 * 150.0 * 2.5) * 1.1; // 2062.5
      
      expect(result, isNotNull);
      expect(result, equals(expectedResult));
    });

    test('should handle invalid formula gracefully', () {
      // Create invalid formula with missing final result variable
      final invalidFormula = CalculationFormulaModel(
        formulaId: 'invalid',
        formulaName: 'Invalid Formula',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Test Step',
            formula: 'totalWeightTons × rateValue',
            resultVariable: 'testResult',
          ),
        ],
        finalResultVariable: 'nonExistentVariable', // This doesn't exist
      );

      const rateValue = 2.5;

      // Calculate using invalid formula
      final result = FlexibleFormulaCalculationService.calculateWithFormula(
        invoice: testInvoice,
        formula: invalidFormula,
        rateValue: rateValue,
      );

      expect(result, isNull);
    });

    test('should test formula with sample data', () {
      // Create standard formula
      final formula = CalculationFormulaModel.fromJson(FormulaTemplates.standardFormula);

      // Test formula
      final testResults = FlexibleFormulaCalculationService.testFormula(formula: formula);

      expect(testResults['success'], isTrue);
      expect(testResults['finalResult'], isNotNull);
      expect(testResults['stepResults'], isNotEmpty);
      expect(testResults['stepDetails'], isNotEmpty);
    });

    test('should validate expression correctly', () {
      const validExpression = 'totalWeightTons × distanceInKilometers × rateValue';
      const invalidExpression = 'totalWeightTons × unknownVariable';

      final availableVariables = FormulaVariables.baseVariables;

      // Test valid expression
      final isValid = FlexibleFormulaCalculationService.validateFormulaExpression(
        validExpression,
        availableVariables,
      );
      expect(isValid, isTrue);

      // Test invalid expression
      final isInvalid = FlexibleFormulaCalculationService.validateFormulaExpression(
        invalidExpression,
        availableVariables,
      );
      expect(isInvalid, isFalse);
    });

    test('should handle division by zero gracefully', () {
      // Create formula with division by zero
      final formula = CalculationFormulaModel(
        formulaId: 'division_test',
        formulaName: 'Division Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Division by Zero',
            formula: 'totalWeightTons ÷ 0',
            resultVariable: 'result',
          ),
        ],
        finalResultVariable: 'result',
      );

      const rateValue = 2.5;

      // Calculate using formula with division by zero
      final result = FlexibleFormulaCalculationService.calculateWithFormula(
        invoice: testInvoice,
        formula: formula,
        rateValue: rateValue,
      );

      expect(result, isNull);
    });

    test('should handle complex parentheses correctly', () {
      // Create formula with complex parentheses
      final formula = CalculationFormulaModel(
        formulaId: 'parentheses_test',
        formulaName: 'Parentheses Test',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Complex Calculation',
            formula: '(totalWeightTons + 1) × (distanceInKilometers - 50) × rateValue',
            resultVariable: 'result',
          ),
        ],
        finalResultVariable: 'result',
      );

      const rateValue = 2.5;

      // Calculate using formula
      final result = FlexibleFormulaCalculationService.calculateWithFormula(
        invoice: testInvoice,
        formula: formula,
        rateValue: rateValue,
      );

      // Expected calculation: (5.0 + 1) × (150 - 50) × 2.5 = 6 × 100 × 2.5 = 1500
      const expectedResult = (5.0 + 1) * (150.0 - 50) * 2.5; // 1500.0
      
      expect(result, isNotNull);
      expect(result, equals(expectedResult));
    });
  });
}
