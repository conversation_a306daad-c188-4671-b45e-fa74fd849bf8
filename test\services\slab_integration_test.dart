import 'package:flutter_test/flutter_test.dart';
import 'package:logestics/models/invoice_model.dart';
import 'package:logestics/models/slab/slab_model.dart';
import 'package:logestics/models/slab/formula_variables.dart';
import 'package:logestics/services/slab_rate_calculation_service.dart';
import 'package:logestics/services/flexible_formula_calculation_service.dart';
import 'package:logestics/services/formula_validation_service.dart';
import 'package:logestics/services/slab_formula_migration_service.dart';

void main() {
  group('Slab Integration Tests', () {
    late InvoiceModel testInvoice;

    setUp(() {
      testInvoice = InvoiceModel(
        invoiceNumber: 1001,
        invoiceStatus: 'Active',
        tasNumber: 'TAS001',
        productName: 'Test Product',
        numberOfBags: 100,
        weightPerBag: 50.0,
        customerName: 'Test Customer',
        truckNumber: 'TRK001',
        conveyNoteNumber: 'CN001',
        biltyNumber: 'BLT001',
        consignorName: 'Test Consignor',
        deliveryMode: 'Road',
        districtId: 'DIST001',
        districtName: 'Test District',
        stationId: 'STN001',
        stationName: 'Test Station',
        fromPlaceId: 'PLC001',
        fromPlaceName: 'Test Place',
        distanceInKilometers: 150.0,
        consignorPickUpAddress: 'Test Address',
      );
    });

    test('should create slab with custom formula', () {
      // Create a slab with custom formula
      final formula =
          CalculationFormulaModel.fromJson(FormulaTemplates.standardFormula);

      final slab = SlabModel(
        slabId: 'test_slab_1',
        slabName: 'Test Slab with Formula',
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate: 2.5,
            nonFuelRate: 1.5,
          ),
        ],
        calculationFormula: formula,
      );

      expect(slab.hasCustomFormula, isTrue);
      expect(slab.calculationMethodDescription, contains('Custom Formula'));
      expect(
          slab.calculationFormula!.formulaName, equals('Standard Calculation'));
    });

    test('should create slab without formula (legacy)', () {
      final slab = SlabModel(
        slabId: 'test_slab_2',
        slabName: 'Test Legacy Slab',
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate: 2.5,
            nonFuelRate: 1.5,
          ),
        ],
      );

      expect(slab.hasCustomFormula, isFalse);
      expect(
          slab.calculationMethodDescription, contains('Standard Calculation'));
    });

    test('should validate formula correctly', () {
      final formula =
          CalculationFormulaModel.fromJson(FormulaTemplates.standardFormula);

      final validation = FormulaValidationService.validateFormula(formula);

      // Print debug information if validation fails
      if (!validation.isValid) {
        print('Validation errors: ${validation.errors}');
        print('Validation warnings: ${validation.warnings}');
      }

      expect(validation.isValid, isTrue);
      expect(validation.errors, isEmpty);
      expect(validation.testResults, isNotNull);
      expect(validation.testResults!['success'], isTrue);
    });

    test('should detect invalid formula', () {
      final invalidFormula = CalculationFormulaModel(
        formulaId: 'invalid',
        formulaName: 'Invalid Formula',
        steps: [
          FormulaStepModel(
            stepId: 'step1',
            stepName: 'Invalid Step',
            formula: 'unknownVariable × invalidVariable',
            resultVariable: 'result',
          ),
        ],
        finalResultVariable: 'result',
      );

      final validation =
          FormulaValidationService.validateFormula(invalidFormula);

      expect(validation.isValid, isFalse);
      expect(validation.errors, isNotEmpty);
    });

    test('should migrate legacy slab to formula system', () {
      // Create legacy slab without formula
      final legacySlab = SlabModel(
        slabId: 'legacy_slab',
        slabName: 'Legacy Slab',
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate: 2.5,
            nonFuelRate: 1.5,
          ),
        ],
      );

      expect(SlabFormulaMigrationService.needsMigration(legacySlab), isTrue);

      // Migrate to formula system
      final migratedSlab =
          SlabFormulaMigrationService.migrateToStandardFormula(legacySlab);

      expect(migratedSlab.hasCustomFormula, isTrue);
      expect(migratedSlab.calculationFormula!.formulaName,
          equals('Standard Calculation'));
      expect(SlabFormulaMigrationService.needsMigration(migratedSlab), isFalse);
    });

    test('should calculate same result for legacy and migrated slab', () {
      // Create legacy slab
      final legacySlab = SlabModel(
        slabId: 'legacy_slab',
        slabName: 'Legacy Slab',
        startDate: DateTime.now().subtract(const Duration(days: 1)),
        expiryDate: DateTime.now().add(const Duration(days: 30)),
        createdAt: DateTime.now(),
        rates: [
          SlabRateModel(
            regionId: 'REG001',
            regionName: 'Test Region',
            districtId: 'DIST001',
            districtName: 'Test District',
            hmtRate: 2.5,
            nonFuelRate: 1.5,
          ),
        ],
      );

      // Migrate to formula system
      final migratedSlab =
          SlabFormulaMigrationService.migrateToStandardFormula(legacySlab);

      // Calculate using both approaches
      const rateValue = 2.5;

      // Legacy calculation (hardcoded)
      final totalWeightKg = testInvoice.numberOfBags * testInvoice.weightPerBag;
      final totalWeightTons = totalWeightKg / 1000;
      final legacyResult =
          totalWeightTons * testInvoice.distanceInKilometers * rateValue;

      // Formula calculation
      final formulaResult =
          FlexibleFormulaCalculationService.calculateWithFormula(
        invoice: testInvoice,
        formula: migratedSlab.calculationFormula!,
        rateValue: rateValue,
      );

      expect(formulaResult, isNotNull);
      expect(formulaResult, equals(legacyResult));
    });

    test('should handle complex multi-step formula', () {
      final complexFormula =
          CalculationFormulaModel.fromJson(FormulaTemplates.complexFormula);

      final result = FlexibleFormulaCalculationService.calculateWithFormula(
        invoice: testInvoice,
        formula: complexFormula,
        rateValue: 2.5,
      );

      expect(result, isNotNull);
      expect(result, greaterThan(0));
    });

    test('should validate expression syntax', () {
      const validExpression =
          'totalWeightTons × distanceInKilometers × rateValue';
      const invalidExpression = 'totalWeightTons × (unclosedParenthesis';

      final availableVariables = FormulaVariables.baseVariables;

      expect(
        FlexibleFormulaCalculationService.validateFormulaExpression(
          validExpression,
          availableVariables,
        ),
        isTrue,
      );

      expect(
        FlexibleFormulaCalculationService.validateFormulaExpression(
          invalidExpression,
          availableVariables,
        ),
        isFalse,
      );
    });

    test('should get migration statistics', () {
      final slabs = [
        SlabModel(
          slabId: 'slab1',
          slabName: 'Legacy Slab 1',
          startDate: DateTime.now(),
          expiryDate: DateTime.now().add(const Duration(days: 30)),
          createdAt: DateTime.now(),
          rates: [],
        ),
        SlabModel(
          slabId: 'slab2',
          slabName: 'Formula Slab 2',
          startDate: DateTime.now(),
          expiryDate: DateTime.now().add(const Duration(days: 30)),
          createdAt: DateTime.now(),
          rates: [],
          calculationFormula: CalculationFormulaModel.fromJson(
              FormulaTemplates.standardFormula),
        ),
      ];

      final stats = SlabFormulaMigrationService.getMigrationStatistics(slabs);

      expect(stats.totalSlabs, equals(2));
      expect(stats.slabsWithFormula, equals(1));
      expect(stats.slabsNeedingMigration, equals(1));
      expect(stats.migrationProgress, equals(0.5));
    });
  });
}
